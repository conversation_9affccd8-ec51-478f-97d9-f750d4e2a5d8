import { Test, TestingModule } from '@nestjs/testing';
import { SkuExtractionService } from '../sku-extraction.service';

describe('SkuExtractionService', () => {
  let service: SkuExtractionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SkuExtractionService],
    }).compile();

    service = module.get<SkuExtractionService>(SkuExtractionService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('parseSku', () => {
    it('should parse standard SKU format', () => {
      const result = service.parseSku('G-CF6-S11-csfloral-Gold');

      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        design: 'csfloral',
        color: 'Gold',
        inlayType: 'clear',
      });
    });

    it('should handle gold inlay design', () => {
      const result = service.parseSku('G-CF6-S11-gsfloral-Silver');

      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        design: 'gsfloral',
        color: 'Silver',
        inlayType: 'gold',
      });
    });

    it('should handle silver inlay design', () => {
      const result = service.parseSku('G-CF6-S11-ssfloral-RoseGold');

      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        design: 'ssfloral',
        color: 'RoseGold',
        inlayType: 'silver',
      });
    });

    it('should return empty object for invalid SKU', () => {
      const result = service.parseSku('invalid-sku');

      expect(result).toEqual({});
    });
  });

  describe('extractCompleteOrderData', () => {
    it('should fill missing data from SKU parsing', () => {
      const orderData = {
        sku: 'G-CF6-S11-csfloral-Gold',
        title: 'ComfortFit Ring with Floral Design',
        source: 'etsy' as const,
      };

      const result = service.extractCompleteOrderData(orderData);

      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        design: 'csfloral',
        color: 'Gold',
        inlayType: 'clear',
      });
    });

    it('should preserve existing data and fill missing fields', () => {
      const orderData = {
        style: 'CF6',
        color: 'Silver',
        sku: 'G-CF6-S11-csfloral-Gold',
        title: 'ComfortFit Ring with Floral Design',
        source: 'etsy' as const,
      };

      const result = service.extractCompleteOrderData(orderData);

      expect(result).toEqual({
        style: 'CF6', // Preserved from input
        color: 'Silver', // Preserved from input
        size: 'S11', // Extracted from SKU
        design: 'csfloral', // Extracted from SKU
        inlayType: 'clear', // Extracted from SKU
      });
    });

    it('should handle descriptive SKU patterns', () => {
      const orderData = {
        sku: 'ComfortFit-6mm-GreenEnchanted-12',
        title: 'ComfortFit Ring with Enchanted Design',
        source: 'etsy' as const,
      };

      const result = service.extractCompleteOrderData(orderData);

      expect(result.style).toBe('CF6');
      expect(result.size).toBe('S12');
      expect(result.color).toBe('Enchanted');
      expect(result.design).toBeUndefined();
    });

    it('should handle BCF6 style', () => {
      const orderData = {
        sku: 'BigComfortFit-6mm-Silver-14',
        title: 'Big ComfortFit Ring',
        source: 'etsy' as const,
      };

      const result = service.extractCompleteOrderData(orderData);

      expect(result.style).toBe('BCF6');
      expect(result.size).toBe('S14');
      expect(result.color).toBe('Silver');
    });

    it('should handle D6 style', () => {
      const orderData = {
        sku: 'Domed-6mm-Gold-10',
        title: 'Domed Ring',
        source: 'etsy' as const,
      };

      const result = service.extractCompleteOrderData(orderData);

      expect(result.style).toBe('D6');
      expect(result.size).toBe('S10');
      expect(result.color).toBe('Gold');
    });
  });

  describe('extractEngravingData', () => {
    it('should extract personalization from variations', () => {
      const variations = [
        {
          property_id: 54,
          formatted_name: 'Personalization',
          formatted_value: 'John & Jane',
        },
        {
          property_id: 513,
          formatted_name: 'Engraving Side',
          formatted_value: 'Inside',
        },
      ];

      const result = service.extractEngravingData(variations);

      expect(result).toEqual({
        personalization: 'John & Jane',
        engravingSide: 'Inside',
        inkColor: undefined,
        outside: {},
        inside: { text: 'John & Jane' },
        isEngraved: true,
      });
    });

    it('should handle outside engraving', () => {
      const variations = [
        {
          property_id: 54,
          formatted_name: 'Personalization',
          formatted_value: 'Forever Together',
        },
        {
          property_id: 513,
          formatted_name: 'Engraving Side',
          formatted_value: 'Outside',
        },
      ];

      const result = service.extractEngravingData(variations);

      expect(result).toEqual({
        personalization: 'Forever Together',
        engravingSide: 'Outside',
        inkColor: undefined,
        outside: { text: 'Forever Together' },
        inside: {},
        isEngraved: true,
      });
    });

    it('should extract ink color from variations', () => {
      const variations = [
        {
          property_id: 100,
          formatted_name: 'Ink Color',
          formatted_value: 'Gold',
        },
      ];

      const result = service.extractEngravingData(variations);

      expect(result.inkColor).toBe('gold');
    });
  });
});
